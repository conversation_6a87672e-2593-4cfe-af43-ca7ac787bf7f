{"name": "localcodeai", "version": "1.0.0", "description": "LocalCodeAI - AI Chat Application for Coding Assistance", "main": "main.js", "homepage": ".", "author": "LocalCodeAI", "license": "MIT", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win --config.win.signAndEditExecutable=false --config.win.signDlls=false", "dist": "electron-builder --publish=never", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "build": {"appId": "com.localcodeai.app", "productName": "LocalCodeAI", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules", "!dist", "!*.md", "!run_app.bat", "!debug-test.js", "!console-debug.js", "!FIXES_SUMMARY.md"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "icons/local_code_ai.ico", "requestedExecutionLevel": "asInvoker", "signAndEditExecutable": false, "signDlls": false}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "LocalCodeAI", "installerIcon": "icons/local_code_ai.ico", "uninstallerIcon": "icons/local_code_ai.ico", "deleteAppDataOnUninstall": false}, "mac": {"target": "dmg", "icon": "icons/local_code_ai.ico"}, "linux": {"target": "AppImage", "icon": "icons/local_code_ai.ico"}}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {}}