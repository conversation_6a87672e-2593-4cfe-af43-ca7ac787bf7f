Set-Location "d:\app_dev\LLM_Chat"

Write-Host "Killing any running Electron processes..."
Get-Process -Name "LocalCodeAI" -ErrorAction SilentlyContinue | Stop-Process -Force
Get-Process -Name "electron" -ErrorAction SilentlyContinue | Stop-Process -Force

Start-Sleep -Seconds 3

Write-Host "Cleaning dist folder..."
if (Test-Path "dist") {
    Remove-Item "dist" -Recurse -Force
}

Write-Host "Building application..."
npm run build-win

Write-Host "Build complete!"
