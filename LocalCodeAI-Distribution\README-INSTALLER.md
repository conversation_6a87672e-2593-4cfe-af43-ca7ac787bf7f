# LocalCodeAI - Desktop Installation Guide

## Overview
LocalCodeAI is a standalone desktop application that provides an AI-powered chat interface for coding assistance. This application runs locally on your Windows PC and connects to your local Ollama instance for AI model inference.

## System Requirements
- **Operating System**: Windows 10 or later (64-bit)
- **RAM**: Minimum 4GB, Recommended 8GB or more
- **Storage**: At least 500MB free space for the application
- **Network**: Internet connection for initial setup and model downloads
- **Dependencies**: Ollama must be installed and running locally

## Prerequisites
Before installing LocalCodeAI, you need to have Ollama installed and running:

1. **Install Ollama**:
   - Download Ollama from: https://ollama.ai/
   - Install and run Ollama on your system
   - Ollama should be accessible at `http://localhost:11434`

2. **Download AI Models**:
   - Open a command prompt or terminal
   - Run: `ollama pull llama2` (or your preferred model)
   - You can also use: `ollama pull codellama`, `ollama pull mistral`, etc.

## Installation Steps

### Method 1: Using the Installer (Recommended)
1. **Download the Installer**:
   - Locate the file: `LocalCodeAI Setup 1.0.0.exe`
   - This is the main installer file

2. **Run the Installer**:
   - Double-click `LocalCodeAI Setup 1.0.0.exe`
   - Windows may show a security warning (this is normal for unsigned applications)
   - Click "More info" then "Run anyway" if prompted
   - Follow the installation wizard:
     - Choose installation directory (default is recommended)
     - Select whether to create desktop and start menu shortcuts
     - Click "Install" to begin installation

3. **Launch the Application**:
   - Use the desktop shortcut, or
   - Find "LocalCodeAI" in your Start Menu, or
   - Navigate to the installation directory and run `LocalCodeAI.exe`

### Method 2: Portable Version
If you prefer not to install, you can use the portable version:
1. Navigate to the `dist/win-unpacked` folder
2. Run `LocalCodeAI.exe` directly
3. The application will run without installation

## Features
- **AI Chat Interface**: Interactive chat with AI models for coding assistance
- **Code Editor Integration**: Built-in code editor with syntax highlighting
- **Multiple Language Support**: JavaScript, Python, HTML, CSS, and more
- **AI Persona Configuration**: Customize AI behavior and expertise areas
- **Chat History**: Export and import chat conversations
- **Smart Code Integration**: Apply AI-generated code directly to the editor
- **Theme Support**: Dark and light mode themes
- **File Attachments**: Upload and analyze code files, documents, and images

## Configuration
1. **First Launch**:
   - The application will automatically detect your local Ollama instance
   - Select your preferred AI model from the dropdown
   - Configure AI persona settings if desired

2. **AI Persona Settings**:
   - Click the persona icon in the top-right corner
   - Choose expertise areas: Programming, Web Development, Game Development, etc.
   - Set custom instructions for the AI
   - Configure response style and behavior

## Usage
1. **Start Chatting**:
   - Type your coding questions or requests in the message input
   - Press Enter or click Send to get AI responses
   - Use the code editor for writing and testing code

2. **File Attachments**:
   - Click the attachment button to upload files
   - Supported formats: PDF, text files, code files, images
   - The AI can analyze and help with uploaded content

3. **Code Integration**:
   - Look for the magic wand (✨) button on AI code responses
   - Click to automatically apply code to the integrated editor
   - Edit and test code directly in the application

## Troubleshooting

### Common Issues
1. **"No models available"**:
   - Ensure Ollama is running (`ollama serve`)
   - Check that models are installed (`ollama list`)
   - Verify Ollama is accessible at `http://localhost:11434`

2. **Application won't start**:
   - Check Windows compatibility (Windows 10+ required)
   - Try running as administrator
   - Check antivirus software isn't blocking the application

3. **Connection errors**:
   - Verify Ollama is running and accessible
   - Check firewall settings
   - Ensure no other applications are using port 11434

### Getting Help
- Check the application's built-in help and attribution panel
- Verify your Ollama installation and model availability
- Ensure your system meets the minimum requirements

## Uninstallation
1. **Using Windows Settings**:
   - Go to Settings > Apps
   - Find "LocalCodeAI" in the list
   - Click and select "Uninstall"

2. **Using Control Panel**:
   - Open Control Panel > Programs and Features
   - Find "LocalCodeAI" and click "Uninstall"

3. **Manual Removal**:
   - Delete the installation directory
   - Remove desktop and start menu shortcuts
   - Clear application data from `%APPDATA%\LocalCodeAI` if desired

## Data and Privacy
- All chat data is stored locally on your computer
- No data is sent to external servers (except to your local Ollama instance)
- Chat history is saved in your browser's local storage
- You can export and import chat history as needed

## Version Information
- **Version**: 1.0.0
- **Build Date**: December 2024
- **Electron Version**: 27.x
- **Platform**: Windows x64

## License
This application is provided as-is for personal and educational use. Please respect the licenses of the underlying technologies (Electron, Ollama, etc.).

---

**Note**: This application requires Ollama to be installed and running separately. LocalCodeAI is a frontend interface that connects to your local Ollama instance for AI model inference.
