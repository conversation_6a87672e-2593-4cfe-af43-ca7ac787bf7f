directories:
  output: dist
  buildResources: build
appId: com.localcodeai.app
productName: LocalCodeAI
files:
  - filter:
      - '**/*'
      - '!node_modules'
      - '!dist'
      - '!*.md'
      - '!run_app.bat'
      - '!debug-test.js'
      - '!console-debug.js'
      - '!FIXES_SUMMARY.md'
win:
  target:
    - target: nsis
      arch:
        - x64
  icon: icons/local_code_ai.ico
  requestedExecutionLevel: asInvoker
  signAndEditExecutable: false
  signDlls: false
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: LocalCodeAI
  installerIcon: icons/local_code_ai.ico
  uninstallerIcon: icons/local_code_ai.ico
  deleteAppDataOnUninstall: false
mac:
  target: dmg
  icon: icons/local_code_ai.ico
linux:
  target: AppImage
  icon: icons/local_code_ai.ico
electronVersion: 27.3.11
