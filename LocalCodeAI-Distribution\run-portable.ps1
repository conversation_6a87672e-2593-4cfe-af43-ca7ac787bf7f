# LocalCodeAI - Portable Version Launcher (PowerShell)
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "LocalCodeAI - Portable Version Launcher" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function to check if Ollama is running
function Test-OllamaConnection {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:11434/api/tags" -TimeoutSec 5 -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

# Check if <PERSON><PERSON><PERSON> is running
Write-Host "Checking if Ollama is running..." -ForegroundColor Yellow
if (Test-OllamaConnection) {
    Write-Host "✓ Ollama is running and accessible" -ForegroundColor Green
    Write-Host ""
} else {
    Write-Host "WARNING: Ollama is not running or not accessible at localhost:11434" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please make sure:" -ForegroundColor Yellow
    Write-Host "1. Ollama is installed" -ForegroundColor White
    Write-Host "2. Ollama is running (run 'ollama serve' in another terminal)" -ForegroundColor White
    Write-Host "3. At least one model is downloaded (e.g., 'ollama pull llama2')" -ForegroundColor White
    Write-Host ""
    Write-Host "Press any key to continue anyway, or Ctrl+C to exit..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    Write-Host ""
}

# Check if the executable exists
$exePath = Join-Path $PSScriptRoot "dist\win-unpacked\LocalCodeAI.exe"
if (-not (Test-Path $exePath)) {
    Write-Host "ERROR: LocalCodeAI.exe not found in dist\win-unpacked\" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please make sure you have built the application first by running:" -ForegroundColor Yellow
    Write-Host "npm run build-win" -ForegroundColor White
    Write-Host ""
    Write-Host "Press any key to exit..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit 1
}

Write-Host "Starting LocalCodeAI..." -ForegroundColor Green
Write-Host ""

# Start the application
try {
    Start-Process -FilePath $exePath -WorkingDirectory (Split-Path $exePath)
    Write-Host "LocalCodeAI has been launched!" -ForegroundColor Green
}
catch {
    Write-Host "ERROR: Failed to start LocalCodeAI" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting steps:" -ForegroundColor Yellow
    Write-Host "1. Check that you have Windows 10 or later" -ForegroundColor White
    Write-Host "2. Try running as administrator" -ForegroundColor White
    Write-Host "3. Check your antivirus software" -ForegroundColor White
    Write-Host "4. Verify the executable exists and is not corrupted" -ForegroundColor White
}

Write-Host ""
Write-Host "Press any key to exit this launcher..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
