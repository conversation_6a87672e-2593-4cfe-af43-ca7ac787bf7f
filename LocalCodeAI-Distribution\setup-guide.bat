@echo off
title LocalCodeAI Setup Guide
color 0A

echo ================================================================
echo                    LocalCodeAI Setup Guide
echo ================================================================
echo.
echo This script will help you set up LocalCodeAI on your system.
echo.
echo Prerequisites:
echo 1. Windows 10 or later (64-bit)
echo 2. At least 4GB RAM (8GB recommended)
echo 3. 500MB free disk space
echo 4. Internet connection for downloading Ollama and models
echo.
echo ================================================================
echo.

:MENU
echo Choose an option:
echo.
echo 1. Check system requirements
echo 2. Install/Check Ollama
echo 3. Download AI models
echo 4. Install LocalCodeAI
echo 5. Run LocalCodeAI (portable)
echo 6. Test complete setup
echo 7. Exit
echo.
set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto CHECK_SYSTEM
if "%choice%"=="2" goto CHECK_OLLAMA
if "%choice%"=="3" goto DOWNLOAD_MODELS
if "%choice%"=="4" goto INSTALL_APP
if "%choice%"=="5" goto RUN_PORTABLE
if "%choice%"=="6" goto TEST_SETUP
if "%choice%"=="7" goto EXIT
goto MENU

:CHECK_SYSTEM
echo.
echo ================================================================
echo                    System Requirements Check
echo ================================================================
echo.

REM Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Windows Version: %VERSION%
if %VERSION% LSS 10.0 (
    echo ❌ WARNING: Windows 10 or later is required
) else (
    echo ✓ Windows version is compatible
)
echo.

REM Check architecture
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    echo ✓ 64-bit architecture detected
) else (
    echo ❌ WARNING: 64-bit Windows is required
)
echo.

REM Check available memory (approximate)
for /f "skip=1" %%p in ('wmic computersystem get TotalPhysicalMemory') do (
    set /a RAM=%%p/1024/1024/1024
    goto :RAM_DONE
)
:RAM_DONE
echo Total RAM: %RAM% GB
if %RAM% LSS 4 (
    echo ❌ WARNING: At least 4GB RAM is recommended
) else (
    echo ✓ Sufficient RAM available
)
echo.

REM Check disk space
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do set FREESPACE=%%a
set /a FREEGB=%FREESPACE:~0,-9%
echo Free disk space: %FREEGB% GB
if %FREEGB% LSS 1 (
    echo ❌ WARNING: At least 1GB free space is recommended
) else (
    echo ✓ Sufficient disk space available
)
echo.

pause
goto MENU

:CHECK_OLLAMA
echo.
echo ================================================================
echo                    Ollama Installation Check
echo ================================================================
echo.

REM Check if Ollama is installed
ollama --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Ollama is installed
    ollama --version
    echo.
    
    REM Check if Ollama is running
    curl -s http://localhost:11434/api/tags >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✓ Ollama is running
    ) else (
        echo ❌ Ollama is not running
        echo To start Ollama, run: ollama serve
    )
) else (
    echo ❌ Ollama is not installed
    echo.
    echo To install Ollama:
    echo 1. Visit: https://ollama.ai/
    echo 2. Download the Windows installer
    echo 3. Run the installer
    echo 4. Restart this script
    echo.
    echo Would you like to open the Ollama website? (y/n)
    set /p open_site="Enter choice: "
    if /i "%open_site%"=="y" start https://ollama.ai/
)
echo.

pause
goto MENU

:DOWNLOAD_MODELS
echo.
echo ================================================================
echo                    Download AI Models
echo ================================================================
echo.

REM Check if Ollama is available
ollama --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Ollama is not installed. Please install Ollama first.
    pause
    goto MENU
)

echo Available models to download:
echo.
echo 1. llama2 (7B) - General purpose, good balance
echo 2. codellama (7B) - Specialized for coding
echo 3. mistral (7B) - Fast and efficient
echo 4. llama2:13b - Larger, more capable
echo 5. phi (2.7B) - Smaller, faster
echo 6. List installed models
echo 7. Back to main menu
echo.
set /p model_choice="Enter your choice (1-7): "

if "%model_choice%"=="1" (
    echo Downloading llama2...
    ollama pull llama2
)
if "%model_choice%"=="2" (
    echo Downloading codellama...
    ollama pull codellama
)
if "%model_choice%"=="3" (
    echo Downloading mistral...
    ollama pull mistral
)
if "%model_choice%"=="4" (
    echo Downloading llama2:13b...
    ollama pull llama2:13b
)
if "%model_choice%"=="5" (
    echo Downloading phi...
    ollama pull phi
)
if "%model_choice%"=="6" (
    echo Installed models:
    ollama list
)
if "%model_choice%"=="7" goto MENU

echo.
pause
goto MENU

:INSTALL_APP
echo.
echo ================================================================
echo                    Install LocalCodeAI
echo ================================================================
echo.

if exist "LocalCodeAI Setup 1.0.0.exe" (
    echo ✓ Installer found: LocalCodeAI Setup 1.0.0.exe
    echo.
    echo Starting installer...
    start "" "LocalCodeAI Setup 1.0.0.exe"
    echo.
    echo The installer has been launched.
    echo Follow the installation wizard to complete setup.
) else (
    echo ❌ Installer not found: LocalCodeAI Setup 1.0.0.exe
    echo.
    echo Please make sure the installer file is in the same directory as this script.
)
echo.

pause
goto MENU

:RUN_PORTABLE
echo.
echo ================================================================
echo                    Run LocalCodeAI (Portable)
echo ================================================================
echo.

if exist "dist\win-unpacked\LocalCodeAI.exe" (
    echo ✓ Portable version found
    echo.
    call run-portable.bat
) else (
    echo ❌ Portable version not found
    echo.
    echo The portable version should be in: dist\win-unpacked\LocalCodeAI.exe
    echo Please build the application first or use the installer.
)
echo.

pause
goto MENU

:TEST_SETUP
echo.
echo ================================================================
echo                    Test Complete Setup
echo ================================================================
echo.

echo Testing Ollama connection...
curl -s http://localhost:11434/api/tags >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Ollama is accessible
    
    echo.
    echo Checking available models...
    ollama list
    
    echo.
    echo ✓ Setup appears to be working correctly!
    echo.
    echo You can now:
    echo 1. Run LocalCodeAI from the Start Menu (if installed)
    echo 2. Use the desktop shortcut (if created)
    echo 3. Run the portable version
    echo.
) else (
    echo ❌ Cannot connect to Ollama
    echo.
    echo Please make sure:
    echo 1. Ollama is installed
    echo 2. Ollama is running (run 'ollama serve')
    echo 3. No firewall is blocking port 11434
)
echo.

pause
goto MENU

:EXIT
echo.
echo Thank you for using LocalCodeAI Setup Guide!
echo.
echo For support and documentation, check the README-INSTALLER.md file.
echo.
pause
exit /b 0
