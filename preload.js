const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // File operations
  saveFile: (data) => ipcRenderer.invoke('save-file', data),
  loadFile: () => ipcRenderer.invoke('load-file'),
  
  // App info
  getAppInfo: () => ipcRenderer.invoke('get-app-info'),

  // Ollama API
  ollamaRequest: (endpoint, method = 'GET', body = null) =>
    ipcRenderer.invoke('ollama-request', { endpoint, method, body }),
  
  // Menu event listeners
  onMenuNewChat: (callback) => ipcRenderer.on('menu-new-chat', callback),
  onMenuExportHistory: (callback) => ipcRenderer.on('menu-export-history', callback),
  onMenuImportHistory: (callback) => ipcRenderer.on('menu-import-history', callback),
  onMenuToggleTheme: (callback) => ipcRenderer.on('menu-toggle-theme', callback),
  onMenuToggleCodeEditor: (callback) => ipcRenderer.on('menu-toggle-code-editor', callback),
  onMenuAIPersona: (callback) => ipcRenderer.on('menu-ai-persona', callback),
  onMenuShowAttributions: (callback) => ipcRenderer.on('menu-show-attributions', callback),
  
  // Remove listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
  
  // Platform info
  platform: process.platform,
  
  // Version info
  versions: {
    node: process.versions.node,
    chrome: process.versions.chrome,
    electron: process.versions.electron
  }
});

// Security: Remove any global Node.js APIs that might have been exposed
delete window.require;
delete window.exports;
delete window.module;
