@echo off
title LocalCodeAI Installation Verification
color 0B

echo ================================================================
echo              LocalCodeAI Installation Verification
echo ================================================================
echo.
echo This script will verify that LocalCodeAI is properly set up
echo and ready to use on your system.
echo.

:CHECK_FILES
echo [1/5] Checking installation files...
echo.

REM Check installer
if exist "LocalCodeAI Setup 1.0.0.exe" (
    echo ✓ Installer found: LocalCodeAI Setup 1.0.0.exe
) else (
    echo ❌ Installer missing: LocalCodeAI Setup 1.0.0.exe
)

REM Check portable version
if exist "Portable\LocalCodeAI.exe" (
    echo ✓ Portable version found: Portable\LocalCodeAI.exe
) else (
    echo ❌ Portable version missing: Portable\LocalCodeAI.exe
)

REM Check documentation
if exist "README-INSTALLER.md" (
    echo ✓ Documentation found: README-INSTALLER.md
) else (
    echo ❌ Documentation missing: README-INSTALLER.md
)

REM Check scripts
if exist "setup-guide.bat" (
    echo ✓ Setup guide found: setup-guide.bat
) else (
    echo ❌ Setup guide missing: setup-guide.bat
)

echo.

:CHECK_SYSTEM
echo [2/5] Checking system requirements...
echo.

REM Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Windows Version: %VERSION%
if %VERSION% LSS 10.0 (
    echo ❌ WARNING: Windows 10 or later is required
    set SYSTEM_OK=0
) else (
    echo ✓ Windows version is compatible
    set SYSTEM_OK=1
)

REM Check architecture
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    echo ✓ 64-bit architecture detected
) else (
    echo ❌ WARNING: 64-bit Windows is required
    set SYSTEM_OK=0
)

echo.

:CHECK_OLLAMA
echo [3/5] Checking Ollama installation...
echo.

REM Check if Ollama is installed
ollama --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Ollama is installed
    for /f "tokens=*" %%a in ('ollama --version 2^>^&1') do echo   Version: %%a
    set OLLAMA_INSTALLED=1
) else (
    echo ❌ Ollama is not installed
    echo   Download from: https://ollama.ai/
    set OLLAMA_INSTALLED=0
)

echo.

:CHECK_OLLAMA_RUNNING
echo [4/5] Checking Ollama service...
echo.

if %OLLAMA_INSTALLED%==1 (
    REM Check if Ollama is running
    curl -s http://localhost:11434/api/tags >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✓ Ollama is running and accessible
        set OLLAMA_RUNNING=1
    ) else (
        echo ❌ Ollama is not running
        echo   Start with: ollama serve
        set OLLAMA_RUNNING=0
    )
) else (
    echo ⏭️ Skipping (Ollama not installed)
    set OLLAMA_RUNNING=0
)

echo.

:CHECK_MODELS
echo [5/5] Checking available models...
echo.

if %OLLAMA_RUNNING%==1 (
    echo Available models:
    ollama list
    
    REM Count models
    for /f %%a in ('ollama list ^| find /c /v ""') do set MODEL_COUNT=%%a
    set /a MODEL_COUNT=%MODEL_COUNT%-1
    
    if %MODEL_COUNT% GTR 0 (
        echo ✓ %MODEL_COUNT% model(s) available
        set MODELS_OK=1
    ) else (
        echo ❌ No models installed
        echo   Download a model: ollama pull llama2
        set MODELS_OK=0
    )
) else (
    echo ⏭️ Skipping (Ollama not running)
    set MODELS_OK=0
)

echo.

:SUMMARY
echo ================================================================
echo                        Verification Summary
echo ================================================================
echo.

REM Calculate overall status
set READY=1

if not defined SYSTEM_OK set SYSTEM_OK=0
if not defined OLLAMA_INSTALLED set OLLAMA_INSTALLED=0
if not defined OLLAMA_RUNNING set OLLAMA_RUNNING=0
if not defined MODELS_OK set MODELS_OK=0

if %SYSTEM_OK%==0 set READY=0
if %OLLAMA_INSTALLED%==0 set READY=0
if %OLLAMA_RUNNING%==0 set READY=0
if %MODELS_OK%==0 set READY=0

echo System Requirements: %SYSTEM_OK%
echo Ollama Installed: %OLLAMA_INSTALLED%
echo Ollama Running: %OLLAMA_RUNNING%
echo Models Available: %MODELS_OK%
echo.

if %READY%==1 (
    echo 🎉 SUCCESS: LocalCodeAI is ready to use!
    echo.
    echo You can now:
    echo • Run the installer: LocalCodeAI Setup 1.0.0.exe
    echo • Use portable version: Run-LocalCodeAI-Portable.bat
    echo • Start chatting with your AI assistant!
    color 0A
) else (
    echo ⚠️  SETUP INCOMPLETE: Some requirements are missing
    echo.
    echo Next steps:
    if %OLLAMA_INSTALLED%==0 echo • Install Ollama from https://ollama.ai/
    if %OLLAMA_RUNNING%==0 echo • Start Ollama service: ollama serve
    if %MODELS_OK%==0 echo • Download a model: ollama pull llama2
    echo.
    echo Run setup-guide.bat for interactive assistance
    color 0E
)

echo.
echo ================================================================
echo.

REM Offer to run setup guide if needed
if %READY%==0 (
    echo Would you like to run the interactive setup guide? (y/n)
    set /p run_setup="Enter choice: "
    if /i "%run_setup%"=="y" (
        echo.
        echo Starting setup guide...
        call setup-guide.bat
    )
)

echo.
echo Press any key to exit...
pause >nul
