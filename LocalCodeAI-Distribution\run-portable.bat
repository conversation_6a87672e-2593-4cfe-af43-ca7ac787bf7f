@echo off
echo ========================================
echo LocalCodeAI - Portable Version Launcher
echo ========================================
echo.

REM Check if <PERSON>lla<PERSON> is running
echo Checking if <PERSON>lla<PERSON> is running...
curl -s http://localhost:11434/api/tags >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Ollama is not running or not accessible at localhost:11434
    echo.
    echo Please make sure:
    echo 1. Ollama is installed
    echo 2. Ollama is running (run 'ollama serve' in another terminal)
    echo 3. At least one model is downloaded (e.g., 'ollama pull llama2')
    echo.
    echo Press any key to continue anyway, or Ctrl+C to exit...
    pause >nul
    echo.
) else (
    echo ✓ Ollama is running and accessible
    echo.
)

REM Check if the executable exists
if not exist "dist\win-unpacked\LocalCodeAI.exe" (
    echo ERROR: LocalCodeAI.exe not found in dist\win-unpacked\
    echo.
    echo Please make sure you have built the application first by running:
    echo npm run build-win
    echo.
    pause
    exit /b 1
)

echo Starting LocalCodeAI...
echo.

REM Start the application
cd /d "%~dp0"
start "" "dist\win-unpacked\LocalCodeAI.exe"

echo LocalCodeAI has been launched!
echo.
echo If the application doesn't start:
echo 1. Check that you have Windows 10 or later
echo 2. Try running as administrator
echo 3. Check your antivirus software
echo.
echo Press any key to exit this launcher...
pause >nul
